[package]
name = "sc_windows"
version = "0.1.0"
edition = "2024"
build = "build.rs"

[dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
windows-numerics = "0.2"
usvg = "0.42"
tiny-skia = "0.11"
resvg = "0.42"
tokio = { version = "1", features = ["rt", "rt-multi-thread"] }
anyhow = "1.0"
paddleocr = { path = "paddleocr" }
base64 = "0.21"
reqwest = { version = "0.11", features = ["json"] }


[target.'cfg(windows)'.dependencies]
windows = { version = "0.61", features = [
  "Win32",
  "Win32_Graphics",
  "Win32_Graphics_Gdi",
  "Win32_System",
  "Win32_System_LibraryLoader",
  "Win32_System_DataExchange",
  "Win32_System_Ole",
  "Win32_System_Memory",
  "Win32_System_Com",
  "Win32_UI",
  "Win32_UI_WindowsAndMessaging",
  "Win32_Graphics_Direct2D",
  "Win32_Graphics_Dxgi_Common",
  "Win32_Media",
  "Foundation_Numerics",
  "Win32_System_Threading",
  "Win32_Graphics_DirectWrite",
  "Win32_Graphics_Direct2D_Common",
  "Win32_UI_Input_KeyboardAndMouse",
  "Win32_UI_Input",
  "Win32_UI_HiDpi",
  "Win32_UI_Shell",
  "Win32_UI_Shell_Common",
  "Win32_UI_Controls_Dialogs",
  "Win32_UI_Controls",
  "Win32_Graphics_Gdi",
  "Win32_System_SystemServices",
] }

[build-dependencies]
embed-resource = "2.4"

[profile.release]
#panic = "abort"
codegen-units = 1
lto = true
#incremental = false
opt-level = 3
