[package]
name = "paddleocr"
version = "0.4.1"
edition = "2021"
license = "MIT"
description = "A simple Rust wrapper for PaddleOCR-JSON."
homepage = "https://github.com/OverflowCat/paddleocr"
repository = "https://github.com/OverflowCat/paddleocr"
readme = "README.md"
categories = ["computer-vision", "external-ffi-bindings"]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
base64 = { version = "0.21.4", optional = true }
serde = { version = "1.0", features = ["derive"] }
serde_json = { version = "1.0" }


[lib]
doctest = false

[features]
bytes = ["dep:base64"]

[dev-dependencies]
paddleocr = { path = ".", features = ["bytes"] }
