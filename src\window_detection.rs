use windows::{
    Win32::{Foundation::*, UI::WindowsAndMessaging::*},
    core::*,
};

/// 窗口信息结构体
#[derive(Debug, Clone)]
pub struct WindowInfo {
    pub hwnd: HWND,
    pub rect: RECT,
    pub title: String,
    pub class_name: String,
    pub is_visible: bool,
    pub is_minimized: bool,
    pub is_child: bool,    // 新增：是否为子窗口
    pub parent_hwnd: HWND, // 新增：父窗口句柄
    pub z_order: i32,      // 新增：Z序（用于层级排序）
}

impl WindowInfo {
    /// 检查点是否在窗口内
    pub fn contains_point(&self, x: i32, y: i32) -> bool {
        x >= self.rect.left && x <= self.rect.right && y >= self.rect.top && y <= self.rect.bottom
    }
}

/// 窗口检测器
#[derive(Debug)]
pub struct WindowDetector {
    windows: Vec<WindowInfo>,
    current_highlighted_window: Option<usize>,
    include_child_windows: bool, // 新增：是否包含子窗口
}

impl WindowDetector {
    pub fn new() -> Self {
        Self {
            windows: Vec::new(),
            current_highlighted_window: None,
            include_child_windows: true, // 默认包含子窗口
        }
    }

    /// 设置是否包含子窗口
    pub fn set_include_child_windows(&mut self, include: bool) {
        self.include_child_windows = include;
    }

    /// 获取所有活动窗口（包括子窗口）
    pub fn refresh_windows(&mut self) -> Result<()> {
        self.windows.clear();

        unsafe {
            // 首先枚举所有顶级窗口
            EnumWindows(
                Some(enum_windows_proc),
                LPARAM(&mut self.windows as *mut _ as isize),
            )?;

            // 如果启用了子窗口检测，为每个顶级窗口枚举其子窗口
            if self.include_child_windows {
                let mut child_windows = Vec::new();
                let top_level_windows: Vec<HWND> = self.windows.iter().map(|w| w.hwnd).collect();

                for &parent_hwnd in &top_level_windows {
                    enum_child_windows_recursive(parent_hwnd, &mut child_windows, 0)?;
                }

                // 将子窗口添加到主列表
                self.windows.extend(child_windows);
            }
        }

        // 过滤掉不需要的窗口
        self.windows.retain(|window| {
            window.is_visible
                && !window.is_minimized
                && window.rect.right > window.rect.left
                && window.rect.bottom > window.rect.top
                && !is_system_window(&window.class_name)
                && !is_invisible_child_window(&window.class_name)
        });

        // 按Z序排序（Z序越小越在前面，即越在顶层）
        self.windows.sort_by_key(|w| w.z_order);

        Ok(())
    }

    /// 根据鼠标位置获取当前应该高亮的窗口（使用WindowFromPoint过滤被覆盖的窗口）
    pub fn get_window_at_point(&mut self, x: i32, y: i32) -> Option<&WindowInfo> {
        // 使用WindowFromPoint获取鼠标位置下的实际窗口
        unsafe {
            let point = POINT { x, y };
            let hwnd_at_point = WindowFromPoint(point);

            if hwnd_at_point.0.is_null() {
                self.current_highlighted_window = None;
                return None;
            }

            // 找到鼠标位置下的所有窗口
            let mut matching_windows = Vec::new();
            for (index, window) in self.windows.iter().enumerate() {
                if window.contains_point(x, y) {
                    matching_windows.push((index, window));
                }
            }

            if matching_windows.is_empty() {
                self.current_highlighted_window = None;
                return None;
            }

            // 首先尝试找到精确匹配的窗口（包括子窗口）
            for (index, window) in &matching_windows {
                if window.hwnd == hwnd_at_point {
                    self.current_highlighted_window = Some(*index);
                    return Some(window);
                }
            }

            // 如果没有精确匹配，查找父窗口链中的匹配
            let mut current_hwnd = hwnd_at_point;
            while !current_hwnd.0.is_null() {
                for (index, window) in &matching_windows {
                    if window.hwnd == current_hwnd {
                        self.current_highlighted_window = Some(*index);
                        return Some(window);
                    }
                }

                // 向上查找父窗口
                match GetParent(current_hwnd) {
                    Ok(parent) => {
                        if parent.0.is_null() {
                            break;
                        }
                        current_hwnd = parent;
                    }
                    Err(_) => break,
                }
            }

            // 如果都没找到，回退到选择面积最小的窗口（通常是最具体的）
            let smallest_window = matching_windows.iter().min_by_key(|(_, window)| {
                let width = window.rect.right - window.rect.left;
                let height = window.rect.bottom - window.rect.top;
                width * height
            });

            if let Some((index, _)) = smallest_window {
                self.current_highlighted_window = Some(*index);
                self.windows.get(*index)
            } else {
                self.current_highlighted_window = None;
                None
            }
        }
    }

    /// 获取当前高亮的窗口
    pub fn get_current_highlighted_window(&self) -> Option<&WindowInfo> {
        if let Some(index) = self.current_highlighted_window {
            self.windows.get(index)
        } else {
            None
        }
    }

    /// 获取所有窗口
    pub fn get_all_windows(&self) -> &Vec<WindowInfo> {
        &self.windows
    }

    /// 过滤掉被完全遮挡的窗口，但保留子窗口
    fn filter_occluded_windows(&mut self) {
        let mut visible_windows = Vec::new();

        // 首先找出所有顶级窗口和子窗口
        let mut top_level_windows = Vec::new();
        let mut child_windows = Vec::new();

        for window in &self.windows {
            if window.is_child {
                child_windows.push(window.clone());
            } else {
                top_level_windows.push(window.clone());
            }
        }

        // 过滤顶级窗口，只保留未被遮挡的
        let mut visible_top_level = Vec::new();

        // 按Z序遍历顶级窗口（从前到后）
        for (i, window) in top_level_windows.iter().enumerate() {
            let mut is_visible = true;

            // 检查是否被前面的窗口完全遮挡
            for j in 0..i {
                let front_window = &top_level_windows[j];

                // 只有当前面的窗口完全包含当前窗口时，才认为被遮挡
                if is_window_completely_covered(window, front_window) {
                    is_visible = false;
                    break;
                }
            }

            if is_visible {
                visible_top_level.push(window.clone());
            }
        }

        // 合并可见的顶级窗口和所有子窗口
        visible_windows.extend(visible_top_level);
        visible_windows.extend(child_windows);

        // 按Z序重新排序
        visible_windows.sort_by_key(|w| w.z_order);

        self.windows = visible_windows;
    }
}

/// EnumWindows的回调函数
unsafe extern "system" fn enum_windows_proc(hwnd: HWND, lparam: LPARAM) -> BOOL {
    unsafe {
        let windows = &mut *(lparam.0 as *mut Vec<WindowInfo>);

        // 获取窗口矩形
        let mut rect = RECT::default();
        if GetWindowRect(hwnd, &mut rect).is_err() {
            return TRUE; // 继续枚举
        }

        // 修正全屏窗口的矩形坐标，确保不超出屏幕边界
        let screen_width = GetSystemMetrics(SM_CXSCREEN);
        let screen_height = GetSystemMetrics(SM_CYSCREEN);

        // 限制窗口矩形在屏幕范围内
        rect.left = rect.left.max(0);
        rect.top = rect.top.max(0);
        rect.right = rect.right.min(screen_width);
        rect.bottom = rect.bottom.min(screen_height);

        // 获取窗口标题
        let mut title_buffer = [0u16; 256];
        let title_len = GetWindowTextW(hwnd, &mut title_buffer);
        let title = if title_len > 0 {
            String::from_utf16_lossy(&title_buffer[..title_len as usize])
        } else {
            String::new()
        };

        // 获取窗口类名
        let mut class_buffer = [0u16; 256];
        let class_len = GetClassNameW(hwnd, &mut class_buffer);
        let class_name = if class_len > 0 {
            String::from_utf16_lossy(&class_buffer[..class_len as usize])
        } else {
            String::new()
        };

        // 检查窗口是否可见和最小化状态
        let is_visible = IsWindowVisible(hwnd).as_bool();
        let is_minimized = IsIconic(hwnd).as_bool();

        // 获取Z序（窗口层级）
        let z_order = get_window_z_order(hwnd);

        let window_info = WindowInfo {
            hwnd,
            rect,
            title,
            class_name,
            is_visible,
            is_minimized,
            is_child: false,                         // 顶级窗口不是子窗口
            parent_hwnd: HWND(std::ptr::null_mut()), // 顶级窗口没有父窗口
            z_order,
        };

        windows.push(window_info);
        TRUE // 继续枚举
    }
}

/// 检查是否为系统窗口（需要过滤掉的窗口）
fn is_system_window(class_name: &str) -> bool {
    const SYSTEM_CLASSES: &[&str] = &[
        "Shell_TrayWnd",              // 任务栏
        "DV2ControlHost",             // 系统控件
        "MsgrIMEWindowClass",         // 输入法
        "SysShadow",                  // 系统阴影
        "Button",                     // 系统按钮
        "Progman",                    // 桌面
        "WorkerW",                    // 桌面工作区
        "Windows.UI.Core.CoreWindow", // UWP应用核心窗口
        "ApplicationFrameWindow",     // UWP应用框架
        "ForegroundStaging",          // 前台暂存
        "MultitaskingViewFrame",      // 多任务视图
        "EdgeUiInputTopWndClass",     // Edge UI
        "NativeHWNDHost",             // 原生HWND主机
        "Chrome_WidgetWin_0",         // Chrome内部窗口（某些版本）
    ];

    SYSTEM_CLASSES
        .iter()
        .any(|&sys_class| class_name.contains(sys_class))
}

/// 获取窗口在屏幕上的实际可见区域（考虑被其他窗口遮挡的情况）
pub fn get_visible_window_region(hwnd: HWND) -> Result<Vec<RECT>> {
    unsafe {
        let mut window_rect = RECT::default();
        GetWindowRect(hwnd, &mut window_rect)?;

        // 简化版本：直接返回窗口矩形
        // 在实际应用中，可以使用更复杂的算法来计算可见区域
        Ok(vec![window_rect])
    }
}

/// 获取窗口的Z序（层级）
fn get_window_z_order(hwnd: HWND) -> i32 {
    unsafe {
        let mut z_order = 0;

        // 获取桌面窗口的第一个子窗口
        if let Ok(mut current_hwnd) = GetTopWindow(None) {
            while !current_hwnd.0.is_null() {
                if current_hwnd == hwnd {
                    return z_order;
                }
                z_order += 1;

                // 获取下一个窗口
                if let Ok(next_hwnd) = GetWindow(current_hwnd, GW_HWNDNEXT) {
                    current_hwnd = next_hwnd;
                } else {
                    break;
                }
            }
        }

        // 如果没找到，返回一个较大的值
        9999
    }
}

/// 递归枚举子窗口
fn enum_child_windows_recursive(
    parent_hwnd: HWND,
    child_windows: &mut Vec<WindowInfo>,
    depth: i32,
) -> Result<()> {
    // 限制递归深度，避免无限递归
    if depth > 10 {
        return Ok(());
    }

    unsafe {
        let _ = EnumChildWindows(
            Some(parent_hwnd),
            Some(enum_child_windows_proc),
            LPARAM(child_windows as *mut _ as isize),
        );
    }

    Ok(())
}

/// 枚举子窗口的回调函数
unsafe extern "system" fn enum_child_windows_proc(hwnd: HWND, lparam: LPARAM) -> BOOL {
    unsafe {
        let child_windows = &mut *(lparam.0 as *mut Vec<WindowInfo>);

        // 获取窗口矩形（屏幕坐标）
        let mut rect = RECT::default();
        if GetWindowRect(hwnd, &mut rect).is_err() {
            return TRUE; // 继续枚举
        }

        // 检查窗口大小是否合理
        let width = rect.right - rect.left;
        let height = rect.bottom - rect.top;
        if width <= 0 || height <= 0 || width > 5000 || height > 5000 {
            return TRUE; // 跳过无效大小的窗口
        }

        // 获取窗口标题
        let mut title_buffer = [0u16; 256];
        let title_len = GetWindowTextW(hwnd, &mut title_buffer);
        let title = if title_len > 0 {
            String::from_utf16_lossy(&title_buffer[..title_len as usize])
        } else {
            String::new()
        };

        // 获取窗口类名
        let mut class_buffer = [0u16; 256];
        let class_len = GetClassNameW(hwnd, &mut class_buffer);
        let class_name = if class_len > 0 {
            String::from_utf16_lossy(&class_buffer[..class_len as usize])
        } else {
            String::new()
        };

        // 检查窗口是否可见
        let is_visible = IsWindowVisible(hwnd).as_bool();
        let is_minimized = IsIconic(hwnd).as_bool();

        // 获取父窗口
        let parent_hwnd = GetParent(hwnd).unwrap_or(HWND(std::ptr::null_mut()));

        // 获取Z序
        let z_order = get_window_z_order(hwnd);

        let window_info = WindowInfo {
            hwnd,
            rect,
            title,
            class_name,
            is_visible,
            is_minimized,
            is_child: true,
            parent_hwnd,
            z_order,
        };

        child_windows.push(window_info);
        TRUE // 继续枚举
    }
}

/// 检查是否为不可见的子窗口类型
fn is_invisible_child_window(class_name: &str) -> bool {
    const INVISIBLE_CHILD_CLASSES: &[&str] = &[
        "ScrollBar",        // 滚动条
        "ComboLBox",        // 组合框列表
        "tooltips_class32", // 工具提示
        "SysHeader32",      // 列表头
                            // 注意：移除了 Edit、Button、SysListView32、SysTreeView32 等，
                            // 因为这些是用户可能想要截图的有用控件
    ];

    INVISIBLE_CHILD_CLASSES
        .iter()
        .any(|&invisible_class| class_name.contains(invisible_class))
}

/// 检查一个窗口是否被另一个窗口完全遮挡
fn is_window_completely_covered(back_window: &WindowInfo, front_window: &WindowInfo) -> bool {
    // 如果前面的窗口不可见，则不能遮挡后面的窗口
    if !front_window.is_visible {
        return false;
    }

    // 如果是同一个窗口，不算遮挡
    if front_window.hwnd == back_window.hwnd {
        return false;
    }

    // 如果后面的窗口是前面窗口的子窗口，不算遮挡
    if back_window.is_child && back_window.parent_hwnd == front_window.hwnd {
        return false;
    }

    // 检查前面的窗口是否完全包含后面的窗口
    // 这里使用稍微宽松的条件，允许1像素的误差
    let tolerance = 1;
    front_window.rect.left <= back_window.rect.left + tolerance
        && front_window.rect.top <= back_window.rect.top + tolerance
        && front_window.rect.right >= back_window.rect.right - tolerance
        && front_window.rect.bottom >= back_window.rect.bottom - tolerance
}
